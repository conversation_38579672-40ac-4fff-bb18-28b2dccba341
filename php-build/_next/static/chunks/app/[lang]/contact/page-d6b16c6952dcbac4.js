(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[680],{2204:(e,r,t)=>{Promise.resolve().then(t.bind(t,5761))},5761:(e,r,t)=>{"use strict";t.d(r,{default:()=>c});var a=t(5155),s=t(2115),o=t(4889),l=t(3478),n=t(5683);function i(e){var r,t,o,i;let{t:c,lang:d}=e,[m,u]=(0,s.useState)({name:"",phone:"",address:"",emergency_type:"",message:"",privacy:!1}),[h,p]=(0,s.useState)({}),[x,g]=(0,s.useState)(!1),[b,f]=(0,s.useState)("idle"),v=()=>{let e={};return m.name.trim()||(e.name="Nome richiesto"),m.phone.trim()?/^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/.test(m.phone)||(e.phone="Numero di telefono non valido"):e.phone="Telefono richiesto",m.address.trim()||(e.address="Indirizzo richiesto"),m.emergency_type||(e.emergency_type="Tipo di emergenza richiesto"),m.privacy||(e.privacy="Accetta la privacy policy"),p(e),0===Object.keys(e).length},w=async e=>{if(e.preventDefault(),v()){g(!0),f("idle");try{let e=m.emergency_type||"Non specificato",r="EMERGENZA: ".concat(e," - ").concat(m.name);if((await fetch("/api/send-email.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:m.name,email:"<EMAIL>",subject:r,message:"RICHIESTA DI EMERGENZA\n\nNome: ".concat(m.name,"\nTelefono: ").concat(m.phone,"\nIndirizzo: ").concat(m.address,"\nTipo di emergenza: ").concat(e,"\nMessaggio: ").concat(m.message,"\n\nInviato dal form di emergenza del sito web."),isEmergency:!0})})).ok)f("success"),u({name:"",phone:"",address:"",emergency_type:"",message:"",privacy:!1});else throw Error("Errore nell'invio dell'email")}catch(e){console.error("Errore durante l'invio dell'email:",e),f("error")}finally{g(!1)}}};return(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"relative overflow-hidden bg-gradient-to-br from-inparoblue-50 via-white to-inparoblue-100 p-8 rounded-2xl shadow-xl border border-inparoblue-100",children:[(0,a.jsx)("div",{className:"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-inparoblue-500/10 to-inparoblue-400/10 rounded-full -mr-16 -mt-16"}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-inparoblue-500/10 to-inparoblue-300/10 rounded-full -ml-12 -mb-12"}),(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-inparoblue-600 to-inparoblue-500 p-3 rounded-full shadow-md mr-4",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:c.contact.emergency})]}),(0,a.jsx)("p",{className:"text-gray-700 mb-8 ml-16",children:c.contact.description}),(0,a.jsxs)("form",{onSubmit:w,className:"space-y-6 relative z-10 mx-auto max-w-3xl",children:[(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-inparoblue-200 to-inparoblue-300 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"}),(0,a.jsxs)("div",{className:"relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition",children:[(0,a.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1 flex items-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-inparoblue-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),c.contact.form.name]}),(0,a.jsx)("input",{type:"text",id:"name",name:"name",value:m.name,onChange:e=>u(r=>({...r,name:e.target.value})),className:"block w-full px-4 py-3 rounded-lg bg-gray-50 border-transparent text-gray-900 focus:border-inparoblue-500 focus:bg-white focus:ring-2 focus:ring-inparoblue-200 transition duration-200 ".concat(h.name?"border-inparoblue-500 ring-2 ring-inparoblue-200":""),placeholder:(null===(r=c.contact.form.placeholders)||void 0===r?void 0:r.name)||"Es. Mario Rossi","aria-invalid":h.name?"true":"false","aria-describedby":h.name?"name-error":void 0}),h.name&&(0,a.jsxs)("p",{id:"name-error",className:"mt-1 text-sm text-inparoblue-600 flex items-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),h.name]})]})]}),(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-inparoblue-200 to-inparoblue-300 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"}),(0,a.jsxs)("div",{className:"relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition",children:[(0,a.jsxs)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-1 flex items-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-inparoblue-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})}),c.contact.form.phone]}),(0,a.jsx)("input",{type:"tel",id:"phone",name:"phone",value:m.phone,onChange:e=>u(r=>({...r,phone:e.target.value})),className:"block w-full px-4 py-3 rounded-lg bg-gray-50 border-transparent text-gray-900 focus:border-inparoblue-500 focus:bg-white focus:ring-2 focus:ring-inparoblue-200 transition duration-200 ".concat(h.phone?"border-inparoblue-500 ring-2 ring-inparoblue-200":""),placeholder:(null===(t=c.contact.form.placeholders)||void 0===t?void 0:t.phone)||"+41 XX XXX XX XX","aria-invalid":h.phone?"true":"false","aria-describedby":h.phone?"phone-error":void 0}),h.phone&&(0,a.jsxs)("p",{id:"phone-error",className:"mt-1 text-sm text-inparoblue-600 flex items-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),h.phone]})]})]}),(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-inparoblue-200 to-inparoblue-300 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"}),(0,a.jsxs)("div",{className:"relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition",children:[(0,a.jsxs)("label",{htmlFor:"address",className:"block text-sm font-medium text-gray-700 mb-1 flex items-center",children:[(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-inparoblue-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),c.contact.form.address||"Indirizzo"]}),(0,a.jsx)("input",{type:"text",id:"address",name:"address",value:m.address,onChange:e=>u(r=>({...r,address:e.target.value})),className:"block w-full px-4 py-3 rounded-lg bg-gray-50 border-transparent text-gray-900 focus:border-inparoblue-500 focus:bg-white focus:ring-2 focus:ring-inparoblue-200 transition duration-200 ".concat(h.address?"border-inparoblue-500 ring-2 ring-inparoblue-200":""),placeholder:(null===(o=c.contact.form.placeholders)||void 0===o?void 0:o.address)||"Via, numero civico, cap, citt\xe0","aria-invalid":h.address?"true":"false","aria-describedby":h.address?"address-error":void 0}),h.address&&(0,a.jsxs)("p",{id:"address-error",className:"mt-1 text-sm text-inparoblue-600 flex items-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),h.address]})]})]}),(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-inparoblue-200 to-inparoblue-300 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"}),(0,a.jsxs)("div",{className:"relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition",children:[(0,a.jsxs)("label",{htmlFor:"emergency_type",className:"block text-sm font-medium text-gray-700 mb-1 flex items-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-inparoblue-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})}),c.contact.form.emergency_type||"Tipo di emergenza"]}),(0,a.jsxs)("select",{id:"emergency_type",name:"emergency_type",value:m.emergency_type,onChange:e=>u(r=>({...r,emergency_type:e.target.value})),className:"block w-full px-4 py-3 rounded-lg bg-gray-50 border-transparent text-gray-900 focus:border-inparoblue-500 focus:bg-white focus:ring-2 focus:ring-inparoblue-200 transition duration-200 ".concat(h.emergency_type?"border-inparoblue-500 ring-2 ring-inparoblue-200":""),"aria-invalid":h.emergency_type?"true":"false","aria-describedby":h.emergency_type?"emergency-type-error":void 0,children:[(0,a.jsx)("option",{value:"",children:"it"===d?"Seleziona il tipo di emergenza":"fr"===d?"S\xe9lectionnez le type d'urgence":"W\xe4hlen Sie den Notfalltyp"}),"it"===d?["Allagamento","Perdita d'acqua","Tubature rotte","Altro"].map(e=>(0,a.jsx)("option",{value:e,children:e},e)):"fr"===d?["Inondation","Fuite d'eau","Tuyaux cass\xe9s","Autre"].map(e=>(0,a.jsx)("option",{value:e,children:e},e)):["\xdcberschwemmung","Wasserleck","Gebrochene Rohre","Andere"].map(e=>(0,a.jsx)("option",{value:e,children:e},e))]}),h.emergency_type&&(0,a.jsxs)("p",{id:"emergency-type-error",className:"mt-1 text-sm text-inparoblue-600 flex items-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),h.emergency_type]})]})]}),(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-inparoblue-200 to-inparoblue-300 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"}),(0,a.jsxs)("div",{className:"relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition",children:[(0,a.jsxs)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-1 flex items-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-inparoblue-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"})}),c.contact.form.message]}),(0,a.jsx)("textarea",{id:"message",name:"message",rows:4,value:m.message,onChange:e=>u(r=>({...r,message:e.target.value})),className:"block w-full px-4 py-3 rounded-lg bg-gray-50 border-transparent text-gray-900 focus:border-inparoblue-500 focus:bg-white focus:ring-2 focus:ring-inparoblue-200 transition duration-200",placeholder:(null===(i=c.contact.form.placeholders)||void 0===i?void 0:i.message)||"Scrivi qui il tuo messaggio..."})]})]}),(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-inparoblue-200 to-inparoblue-300 rounded-lg blur opacity-30 group-hover:opacity-50 transition duration-300"}),(0,a.jsx)("div",{className:"relative bg-white p-5 rounded-lg shadow-sm hover:shadow transition",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex items-center h-5",children:(0,a.jsx)("input",{id:"privacy",name:"privacy",type:"checkbox",checked:m.privacy,onChange:e=>u(r=>({...r,privacy:e.target.checked})),className:"h-5 w-5 rounded border-transparent bg-gray-100 text-inparoblue-600 focus:ring-inparoblue-400 transition-colors ".concat(h.privacy?"border-inparoblue-500 ring-2 ring-inparoblue-200":""),"aria-invalid":h.privacy?"true":"false","aria-describedby":h.privacy?"privacy-error":void 0})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsxs)("label",{htmlFor:"privacy",className:"text-sm font-medium text-gray-700 flex items-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2 text-inparoblue-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})}),"it"===d?"Accetto la privacy policy":"fr"===d?"J'accepte la politique de confidentialit\xe9":"Ich akzeptiere die Datenschutzrichtlinie"]}),h.privacy&&(0,a.jsxs)("p",{id:"privacy-error",className:"mt-1 text-sm text-inparoblue-600 flex items-center ml-6",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),h.privacy]})]})]})})]}),(0,a.jsxs)("div",{className:"relative group mt-8",children:[(0,a.jsx)("div",{className:"absolute -inset-1 bg-gradient-to-r from-inparoblue-600 to-inparoblue-500 rounded-lg blur opacity-40 group-hover:opacity-75 transition duration-300 animate-pulse",style:{zIndex:0}}),(0,a.jsx)(l.P.button,{type:"submit",disabled:x,className:"relative w-full flex justify-center items-center py-4 px-6 border border-transparent rounded-lg text-base font-bold text-white bg-gradient-to-r from-inparoblue-600 to-inparoblue-500 shadow-lg hover:from-inparoblue-500 hover:to-inparoblue-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-inparoblue-500 disabled:from-gray-400 disabled:to-gray-300 disabled:cursor-not-allowed transition-all duration-300 hover:shadow-xl z-10",whileHover:{translateY:-2},whileTap:{scale:.98},children:x?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(l.P.div,{className:"h-5 w-5 mr-3 border-2 border-white border-t-transparent rounded-full",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),(0,a.jsx)("span",{children:"Invio in corso..."})]}):(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 14l5-5-5-5"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9H5"})]}),c.contact.form.submit||"Invia richiesta"]})})]})]}),(0,a.jsx)(n.N,{children:"idle"!==b&&(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{type:"spring",damping:20},className:"mt-8 p-6 rounded-xl shadow-lg border ".concat("success"===b?"bg-gradient-to-r from-green-50 to-green-100 border-green-200":"bg-gradient-to-r from-inparoblue-50 to-inparoblue-100 border-inparoblue-200"),children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 p-3 rounded-full ".concat("success"===b?"bg-green-100":"bg-inparoblue-100"),children:"success"===b?(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-green-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}):(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-inparoblue-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-bold ".concat("success"===b?"text-green-800":"text-inparoblue-800"),children:"success"===b?"Richiesta inviata con successo!":"Errore durante l'invio"}),(0,a.jsx)("p",{className:"mt-1 text-sm ".concat("success"===b?"text-green-700":"text-inparoblue-700"),children:"success"===b?"Ti contatteremo al pi\xf9 presto.":"Si \xe8 verificato un errore. Riprova pi\xf9 tardi."})]})]}),"success"===b&&(0,a.jsx)("div",{className:"mt-4 flex justify-end",children:(0,a.jsx)(l.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>f("idle"),className:"px-4 py-2 text-sm font-medium text-green-700 bg-green-100 rounded-md hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:"Chiudi messaggio"})})]})})]})}function c(e){var r,t,l,n;let{t:c,lang:d}=e,[m,u]=(0,s.useState)("general"),[h,p]=(0,s.useState)(!1),[x,g]=(0,s.useState)(null),[b,f]=(0,s.useState)({name:"",email:"",subject:"",message:"",privacy:!1}),v=e=>{let{name:r,value:t}=e.target;f({...b,[r]:t})},w=async e=>{if(e.preventDefault(),!b.email||!b.message){g({success:!1,message:"Per favore, compila tutti i campi obbligatori."});return}if(!b.privacy){g({success:!1,message:"Devi accettare la privacy policy per procedere."});return}p(!0),g(null);try{let e=await fetch("/api/send-email.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:b.name,email:b.email,subject:b.subject||"Richiesta informazioni",message:b.message,isEmergency:!1})}),r=await e.json();e.ok?(g({success:!0,message:"Grazie! Il tuo messaggio \xe8 stato inviato con successo. Ti risponderemo al pi\xf9 presto."}),f({name:"",email:"",subject:"",message:"",privacy:!1})):g({success:!1,message:r.error||"Si \xe8 verificato un errore durante l'invio del messaggio. Riprova pi\xf9 tardi."})}catch(e){console.error("Errore nell'invio del form:",e),g({success:!1,message:"Si \xe8 verificato un errore durante l'invio del messaggio. Riprova pi\xf9 tardi."})}finally{p(!1)}};return(0,a.jsxs)("main",{children:[(0,a.jsxs)("section",{className:"relative h-[75vh] min-h-[550px] overflow-hidden bg-gradient-to-br from-inparoblue-900 via-inparoblue-700 to-inparoblue-500 pt-16",children:[(0,a.jsxs)("div",{className:"absolute inset-0 z-0",children:[(0,a.jsx)("div",{className:"absolute w-full h-full opacity-20",style:{backgroundImage:'url("/images/noise.png")'}}),(0,a.jsx)("div",{className:"absolute top-0 left-0 right-0 h-1/3 bg-gradient-to-b from-blue-500/10 to-transparent"}),(0,a.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"radial-gradient(circle at 30% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 25%), radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.08) 0%, transparent 25%)"}}),(0,a.jsx)("div",{className:"absolute -top-24 -right-24 w-96 h-96 bg-red-500 rounded-full mix-blend-soft-light filter blur-3xl opacity-30 animate-blob animation-delay-2000"}),(0,a.jsx)("div",{className:"absolute top-1/3 -left-20 w-72 h-72 bg-blue-500 rounded-full mix-blend-soft-light filter blur-3xl opacity-30 animate-blob animation-delay-4000"}),(0,a.jsx)("div",{className:"absolute -bottom-32 left-1/3 w-80 h-80 bg-purple-500 rounded-full mix-blend-soft-light filter blur-3xl opacity-30 animate-blob"}),(0,a.jsx)("div",{className:"absolute top-1/2 right-1/4 w-64 h-64 bg-pink-500 rounded-full mix-blend-soft-light filter blur-3xl opacity-30 animate-blob animation-delay-3000"}),(0,a.jsxs)("div",{className:"absolute inset-0 overflow-hidden opacity-20",children:[(0,a.jsx)("div",{className:"absolute top-[10%] left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-blue-400 to-transparent animate-pulse-slow"}),(0,a.jsx)("div",{className:"absolute top-[30%] left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-red-400 to-transparent animate-pulse-slow animation-delay-2000"}),(0,a.jsx)("div",{className:"absolute top-[70%] left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-purple-400 to-transparent animate-pulse-slow animation-delay-3000"}),(0,a.jsx)("div",{className:"absolute bottom-[20%] left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-indigo-400 to-transparent animate-pulse-slow animation-delay-4000"})]})]}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 z-10",children:(0,a.jsxs)("div",{className:"relative h-16",children:[(0,a.jsx)("div",{className:"absolute top-0 inset-x-0 h-8 bg-gradient-to-b from-transparent to-white/10"}),(0,a.jsx)("div",{className:"absolute top-8 inset-x-0 h-[1px] bg-white/20"}),(0,a.jsx)("div",{className:"absolute bottom-0 inset-x-0 h-8 bg-white",style:{clipPath:"polygon(0 100%, 100% 100%, 100% 0)"}}),(0,a.jsx)("div",{className:"absolute bottom-0 inset-x-0 h-8 bg-white",style:{clipPath:"polygon(0 100%, 0 0, 100% 100%)"}})]})}),(0,a.jsx)("div",{className:"container max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 h-full flex flex-col justify-center",children:(0,a.jsxs)("div",{className:"text-center max-w-3xl mx-auto",children:[(0,a.jsx)(o.A,{delay:.2,children:(0,a.jsxs)("div",{className:"inline-flex items-center px-4 py-1.5 rounded-full bg-gradient-to-r from-inparoblue-500 to-inparoblue-600 text-white text-sm font-medium mb-5 shadow-lg border border-white/10 backdrop-blur-sm relative overflow-hidden group",children:[(0,a.jsx)("span",{className:"absolute inset-0 bg-gradient-to-r from-inparoblue-500/20 to-inparoblue-600/20 group-hover:opacity-80 opacity-0 transition-opacity duration-300"}),(0,a.jsx)("span",{className:"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full animate-shimmer"}),(0,a.jsxs)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("path",{d:"M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"}),(0,a.jsx)("path",{d:"M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"})]}),(0,a.jsx)("span",{className:"ml-2",children:"Contatti"})]})}),(0,a.jsx)(o.A,{delay:.4,children:(0,a.jsx)("h1",{className:"text-5xl md:text-7xl font-manrope font-bold tracking-tighter leading-tight mb-6 text-white text-shadow-glow",children:(0,a.jsxs)("span",{className:"relative",children:[(0,a.jsx)("span",{className:"relative z-10 inline-block",children:c.contact.title}),(0,a.jsx)("span",{className:"absolute -inset-0.5 bg-gradient-to-r from-inparoblue-500 to-inparoblue-600 blur-2xl opacity-20 rounded-full z-0"})]})})}),(0,a.jsx)(o.A,{delay:.6,children:(0,a.jsxs)("div",{className:"relative h-[3px] w-40 mx-auto my-8 overflow-hidden rounded-full",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-inparoblue-500 via-inparoblue-600 to-inparoblue-700"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-inparoblue-700 via-inparoblue-500 to-inparoblue-400 animate-gradient-x"}),(0,a.jsx)("div",{className:"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white to-transparent -translate-x-full animate-shimmer"})]})}),(0,a.jsx)(o.A,{delay:.8,children:(0,a.jsx)("p",{className:"text-base md:text-xl font-sans leading-relaxed text-white/90 max-w-2xl mx-auto mb-10",children:c.contact.description})}),(0,a.jsx)(o.A,{delay:1,children:(0,a.jsxs)("div",{className:"flex flex-wrap justify-center gap-4 mt-8",children:[(0,a.jsxs)("button",{className:"px-6 py-3 bg-gradient-to-r from-inparoblue-500 to-inparoblue-600 hover:from-inparoblue-600 hover:to-inparoblue-700 text-white rounded-full shadow-lg shadow-inparoblue-500/30 transition-all duration-300 mr-3 flex items-center group",children:[(0,a.jsx)("span",{className:"mr-2",children:c.contact.generalInquiry}),(0,a.jsx)("svg",{className:"w-5 h-5 transform group-hover:translate-x-1 transition-transform",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 7l5 5m0 0l-5 5m5-5H6"})})]}),(0,a.jsxs)("button",{className:"px-6 py-3 bg-gradient-to-r from-inparoblue-600 to-inparoblue-700 hover:from-inparoblue-700 hover:to-inparoblue-800 text-white rounded-full shadow-lg shadow-inparoblue-500/30 transition-all duration-300 flex items-center group",children:[(0,a.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsx)("span",{children:c.common.emergency})]})]})})]})})]}),(0,a.jsxs)("section",{className:"bg-white py-20 relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 -z-10 opacity-60",children:[(0,a.jsx)("div",{className:"absolute -top-20 -left-20 w-64 h-64 bg-blue-50 rounded-full filter blur-3xl opacity-40"}),(0,a.jsx)("div",{className:"absolute -bottom-20 -right-20 w-64 h-64 bg-red-50 rounded-full filter blur-3xl opacity-40"}),(0,a.jsx)("div",{className:"absolute top-1/2 left-1/4 w-48 h-48 bg-purple-50 rounded-full filter blur-3xl opacity-30"}),(0,a.jsx)("div",{className:"absolute w-full h-full opacity-10",style:{backgroundImage:'url("/images/noise.png")'}})]}),(0,a.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsx)("div",{className:"max-w-3xl mx-auto",children:(0,a.jsx)("div",{className:"mb-14 relative z-10",children:(0,a.jsxs)("div",{className:"bg-white p-2 sm:p-2.5 rounded-2xl shadow-2xl border border-gray-100 flex flex-col sm:flex-row justify-center max-w-lg mx-auto overflow-hidden backdrop-blur-sm gap-2",children:[(0,a.jsxs)("button",{onClick:()=>u("general"),className:"\n                    flex-1 rounded-xl px-5 py-4 text-base font-medium transition-all duration-300 flex items-center justify-center gap-3\n                    ".concat("general"===m?"bg-gradient-to-r from-inparoblue-600 to-inparoblue-700 text-white shadow-lg transform scale-[1.02]":"text-gray-700 hover:bg-gray-50 border border-gray-100 hover:border-gray-200","\n                  "),"aria-pressed":"general"===m,"aria-label":"Seleziona richiesta generale",children:[(0,a.jsx)("div",{className:"rounded-full p-1.5 bg-white/10 flex items-center justify-center",children:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,a.jsx)("span",{className:"font-medium",children:c.contact.generalInquiry})]}),(0,a.jsxs)("button",{onClick:()=>u("emergency"),className:"\n                    flex-1 rounded-xl px-5 py-4 text-base font-medium transition-all duration-300 flex items-center justify-center gap-3\n                    ".concat("emergency"===m?"bg-gradient-to-r from-inparoblue-600 to-inparoblue-700 text-white shadow-lg transform scale-[1.02]":"text-gray-700 hover:bg-gray-50 border border-gray-100 hover:border-gray-200","\n                  "),"aria-pressed":"emergency"===m,"aria-label":"Seleziona richiesta emergenza",children:[(0,a.jsx)("div",{className:"rounded-full p-1.5 bg-white/10 flex items-center justify-center",children:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,a.jsx)("span",{className:"font-medium",children:c.contact.emergency})]})]})})}),(0,a.jsxs)("div",{className:"mt-12 relative",children:[(0,a.jsx)("div",{className:"absolute -top-6 -right-6 w-32 h-32 bg-inparoblue-50 rounded-full mix-blend-multiply filter blur-2xl opacity-70"}),(0,a.jsx)("div",{className:"absolute -bottom-6 -left-6 w-32 h-32 bg-inparoblue-100 rounded-full mix-blend-multiply filter blur-2xl opacity-70"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-inparoblue-50 via-white to-inparoblue-50 opacity-40 -z-10 rounded-3xl transform -rotate-1 scale-105"}),(0,a.jsxs)("div",{className:"overflow-hidden rounded-3xl bg-white shadow-xl border border-gray-100 backdrop-blur-sm",children:[(0,a.jsx)("div",{className:"w-full h-2 bg-gradient-to-r from-inparoblue-600 to-inparoblue-700"}),(0,a.jsx)("div",{className:"px-6 py-10 sm:p-12",children:"emergency"===m?(0,a.jsx)(i,{t:c,lang:d}):(0,a.jsxs)("form",{className:"space-y-8",onSubmit:e=>e.preventDefault(),children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"col-span-1 group",children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-red-600",children:c.contact.form.name}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3.5 flex items-center pointer-events-none transition-colors group-focus-within:text-red-500",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-400 group-focus-within:text-red-500 transition-colors",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z",clipRule:"evenodd"})})}),(0,a.jsx)("input",{type:"text",name:"name",id:"name",className:"pl-10 block w-full rounded-xl border-gray-200 shadow-sm focus:border-red-500 focus:ring-red-500 sm:text-sm transition-all duration-300 py-3 bg-gray-50 focus:bg-white text-gray-800",placeholder:(null===(r=c.contact.form.placeholders)||void 0===r?void 0:r.name)||"",value:b.name,onChange:v})]})]}),(0,a.jsxs)("div",{className:"col-span-1 group",children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-red-600",children:c.contact.form.email}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3.5 flex items-center pointer-events-none",children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-400 group-focus-within:text-red-500 transition-colors",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,a.jsx)("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),(0,a.jsx)("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})]})}),(0,a.jsx)("input",{type:"email",name:"email",id:"email",className:"pl-10 block w-full rounded-xl border-gray-200 shadow-sm focus:border-red-500 focus:ring-red-500 sm:text-sm transition-all duration-300 py-3 bg-gray-50 focus:bg-white text-gray-800",placeholder:(null===(t=c.contact.form.placeholders)||void 0===t?void 0:t.email)||"",value:b.email,onChange:v,required:!0})]})]})]}),(0,a.jsxs)("div",{className:"group",children:[(0,a.jsx)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-red-600",children:c.contact.form.subject}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3.5 flex items-center pointer-events-none",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-400 group-focus-within:text-red-500 transition-colors",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z",clipRule:"evenodd"})})}),(0,a.jsx)("input",{type:"text",name:"subject",id:"subject",className:"pl-10 block w-full rounded-xl border-gray-200 shadow-sm focus:border-red-500 focus:ring-red-500 sm:text-sm transition-all duration-300 py-3 bg-gray-50 focus:bg-white text-gray-800",placeholder:(null===(l=c.contact.form.placeholders)||void 0===l?void 0:l.subject)||"",value:b.subject,onChange:v})]})]}),(0,a.jsxs)("div",{className:"group",children:[(0,a.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2 transition-colors group-focus-within:text-red-600",children:c.contact.form.message}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute top-3 left-3.5 flex items-start pointer-events-none",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-400 group-focus-within:text-red-500 transition-colors",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 5v8a2 2 0 01-2 2h-5l-5 4v-4H4a2 2 0 01-2-2V5a2 2 0 012-2h12a2 2 0 012 2zM7 8H5v2h2V8zm2 0h2v2H9V8zm6 0h-2v2h2V8z",clipRule:"evenodd"})})}),(0,a.jsx)("textarea",{id:"message",name:"message",rows:5,className:"pl-10 block w-full rounded-xl border-gray-200 shadow-sm focus:border-red-500 focus:ring-red-500 sm:text-sm transition-all duration-300 bg-gray-50 focus:bg-white text-gray-800",placeholder:(null===(n=c.contact.form.placeholders)||void 0===n?void 0:n.message)||"",value:b.message,onChange:v,required:!0})]})]}),(0,a.jsxs)("div",{className:"flex items-start p-4 bg-gray-50 rounded-xl border border-gray-100",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:(0,a.jsxs)("div",{className:"relative flex items-center justify-center h-6 w-6",children:[(0,a.jsx)("input",{id:"privacy",name:"privacy",type:"checkbox",className:"h-5 w-5 text-red-600 focus:ring-red-500 focus:ring-offset-0 border-gray-300 rounded cursor-pointer",checked:b.privacy,onChange:e=>{let{name:r,checked:t}=e.target;f({...b,[r]:t})},required:!0}),(0,a.jsx)("svg",{className:"absolute h-6 w-6 text-red-500 opacity-0 peer-checked:opacity-100 transform scale-90 transition-all duration-200",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("circle",{cx:"12",cy:"12",r:"12",fill:"currentColor",fillOpacity:"0.2"})})]})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("label",{htmlFor:"privacy",className:"text-sm font-medium text-gray-700 cursor-pointer",children:c.contact.form.privacy}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["it"===d?"I tuoi dati saranno trattati secondo la nostra ":"fr"===d?"Vos donn\xe9es seront trait\xe9es conform\xe9ment \xe0 notre ":"Ihre Daten werden gem\xe4\xdf unserer ",(0,a.jsx)("a",{href:"/".concat(d,"/privacy-policy"),className:"text-red-600 hover:text-red-800 underline transition-colors",children:"it"===d?"Informativa sulla Privacy":"fr"===d?"Politique de Confidentialit\xe9":"Datenschutzerkl\xe4rung"})]})]})]}),(0,a.jsx)("div",{className:"pt-4",children:(0,a.jsx)("button",{type:"submit",className:"w-full flex justify-center items-center rounded-xl bg-gradient-to-r from-inparoblue-500 to-inparoblue-600 px-6 py-4 text-base font-semibold text-white shadow-lg hover:from-inparoblue-600 hover:to-inparoblue-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-inparoblue-600 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-xl disabled:opacity-70 disabled:cursor-not-allowed",disabled:h,onClick:w,children:h?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,a.jsx)("span",{children:"Invio in corso..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{children:c.contact.form.submit}),(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"ml-2 h-5 w-5 animate-pulse",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]})})}),x&&(0,a.jsx)("div",{className:"mt-4 p-4 rounded-lg ".concat(x.success?"bg-green-50 text-green-800":"bg-red-50 text-red-800"),children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:x.success?(0,a.jsx)("svg",{className:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}):(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm font-medium",children:x.message})})]})})]})})]})]})]})]}),(0,a.jsxs)("section",{className:"relative overflow-hidden bg-gradient-to-br from-gray-50 to-white py-20 mt-10",children:[(0,a.jsx)("div",{className:"absolute -top-10 -right-10 w-40 h-40 bg-red-50 rounded-full filter blur-3xl opacity-60"}),(0,a.jsx)("div",{className:"absolute -bottom-10 -left-10 w-40 h-40 bg-blue-50 rounded-full filter blur-3xl opacity-60"}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-14",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 sm:text-4xl mb-4",children:c.contact.howToReachUs.title}),(0,a.jsx)("div",{className:"h-1 w-16 mx-auto bg-gradient-to-r from-inparoblue-600 to-inparoblue-700 rounded-full my-4"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:c.contact.howToReachUs.description})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 xl:gap-12",children:[(0,a.jsxs)("div",{className:"group bg-white p-8 rounded-2xl shadow-lg border border-gray-100 flex flex-col items-center text-center transform transition-all duration-500 hover:shadow-xl hover:-translate-y-2 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute top-0 inset-x-0 h-2 bg-gradient-to-r from-inparoblue-600 to-inparoblue-700 transform transition-transform duration-300 group-hover:scale-x-100"}),(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-inparoblue-600 to-inparoblue-700 rounded-2xl flex items-center justify-center mb-6 transform rotate-3 group-hover:rotate-6 transition-all duration-300 shadow-md",children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]})}),(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:c.contact.howToReachUs.office}),(0,a.jsxs)("p",{className:"text-gray-600 mb-4",children:["Inparo Gmbh",(0,a.jsx)("br",{}),"Gubelstrasse 15",(0,a.jsx)("br",{}),"6300 Zug, Schweiz"]}),(0,a.jsxs)("a",{href:"https://maps.google.com",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-sm font-medium text-red-600 hover:text-purple-700 transition-colors",children:[(0,a.jsx)("span",{children:c.contact.howToReachUs.viewOnMaps}),(0,a.jsx)("svg",{className:"ml-1 w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]})]}),(0,a.jsxs)("div",{className:"group bg-white p-8 rounded-2xl shadow-lg border border-gray-100 flex flex-col items-center text-center transform transition-all duration-500 hover:shadow-xl hover:-translate-y-2 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute top-0 inset-x-0 h-2 bg-gradient-to-r from-inparoblue-600 to-inparoblue-700 transform transition-transform duration-300 group-hover:scale-x-100"}),(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-inparoblue-600 to-inparoblue-700 rounded-2xl flex items-center justify-center mb-6 transform -rotate-3 group-hover:rotate-0 transition-all duration-300 shadow-md",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2v-3a2 2 0 00-2-2H5a2 2 0 00-2 2v3a2 2 0 002 2z"})})}),(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:c.contact.howToReachUs.email}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"<EMAIL>"}),(0,a.jsxs)("a",{href:"mailto:<EMAIL>",className:"inline-flex items-center text-sm font-medium text-red-600 hover:text-purple-700 transition-colors",children:[(0,a.jsx)("span",{children:c.contact.howToReachUs.sendEmail}),(0,a.jsx)("svg",{className:"ml-1 w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]})]}),(0,a.jsxs)("div",{className:"group bg-white p-8 rounded-2xl shadow-lg border border-gray-100 flex flex-col items-center text-center transform transition-all duration-500 hover:shadow-xl hover:-translate-y-2 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute top-0 inset-x-0 h-2 bg-gradient-to-r from-inparoblue-600 to-inparoblue-700 transform transition-transform duration-300 group-hover:scale-x-100"}),(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-inparoblue-600 to-inparoblue-700 rounded-2xl flex items-center justify-center mb-6 transform rotate-3 group-hover:-rotate-6 transition-all duration-300 shadow-md",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})})}),(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:c.contact.howToReachUs.phone}),(0,a.jsxs)("p",{className:"text-gray-600 mb-4",children:["+41 76 466 21 22",(0,a.jsx)("br",{}),"Mo-Fr: 9:00-18:00"]}),(0,a.jsxs)("a",{href:"tel:+41764662122",className:"inline-flex items-center text-sm font-medium text-red-600 hover:text-purple-700 transition-colors",children:[(0,a.jsx)("span",{children:c.contact.howToReachUs.callNow}),(0,a.jsx)("svg",{className:"ml-1 w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]}),(0,a.jsxs)("a",{href:"https://wa.me/41764662122",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-sm font-medium text-green-600 hover:text-green-700 transition-colors",children:[(0,a.jsx)("span",{children:c.contact.howToReachUs.whatsapp}),(0,a.jsx)("svg",{className:"ml-1 w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]})]})]})]}),(0,a.jsx)("div",{className:"absolute left-0 right-0 bottom-0 h-8 overflow-hidden",children:(0,a.jsx)("svg",{className:"w-full h-full",viewBox:"0 0 1200 120",preserveAspectRatio:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{d:"M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V120H0V95.72C57.71,118.68,121.41,111.23,165.53,101.1Z",fill:"#ffffff"})})})]})]})}},4889:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var a=t(5155),s=t(3478);function o(e){let{children:r,className:t="",delay:o=0}=e;return(0,a.jsx)(s.P.div,{className:t,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:o},children:r})}},5683:(e,r,t)=>{"use strict";t.d(r,{N:()=>b});var a=t(5155),s=t(2115),o=t(4710),l=t(9234),n=t(9656),i=t(7249);class c extends s.Component{getSnapshotBeforeUpdate(e){let r=this.props.childRef.current;if(r&&e.isPresent&&!this.props.isPresent){let e=r.offsetParent,t=e instanceof HTMLElement&&e.offsetWidth||0,a=this.props.sizeRef.current;a.height=r.offsetHeight||0,a.width=r.offsetWidth||0,a.top=r.offsetTop,a.left=r.offsetLeft,a.right=t-a.width-a.left}return null}componentDidUpdate(){}render(){return this.props.children}}function d(e){let{children:r,isPresent:t,anchorX:o}=e,l=(0,s.useId)(),n=(0,s.useRef)(null),d=(0,s.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:m}=(0,s.useContext)(i.Q);return(0,s.useInsertionEffect)(()=>{let{width:e,height:r,top:a,left:s,right:i}=d.current;if(t||!n.current||!e||!r)return;n.current.dataset.motionPopId=l;let c=document.createElement("style");return m&&(c.nonce=m),document.head.appendChild(c),c.sheet&&c.sheet.insertRule('\n          [data-motion-pop-id="'.concat(l,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(r,"px !important;\n            ").concat("left"===o?"left: ".concat(s):"right: ".concat(i),"px !important;\n            top: ").concat(a,"px !important;\n          }\n        ")),()=>{document.head.removeChild(c)}},[t]),(0,a.jsx)(c,{isPresent:t,childRef:n,sizeRef:d,children:s.cloneElement(r,{ref:n})})}let m=e=>{let{children:r,initial:t,isPresent:o,onExitComplete:i,custom:c,presenceAffectsLayout:m,mode:h,anchorX:p}=e,x=(0,l.M)(u),g=(0,s.useId)(),b=(0,s.useCallback)(e=>{for(let r of(x.set(e,!0),x.values()))if(!r)return;i&&i()},[x,i]),f=(0,s.useMemo)(()=>({id:g,initial:t,isPresent:o,custom:c,onExitComplete:b,register:e=>(x.set(e,!1),()=>x.delete(e))}),m?[Math.random(),b]:[o,b]);return(0,s.useMemo)(()=>{x.forEach((e,r)=>x.set(r,!1))},[o]),s.useEffect(()=>{o||x.size||!i||i()},[o]),"popLayout"===h&&(r=(0,a.jsx)(d,{isPresent:o,anchorX:p,children:r})),(0,a.jsx)(n.t.Provider,{value:f,children:r})};function u(){return new Map}var h=t(5087);let p=e=>e.key||"";function x(e){let r=[];return s.Children.forEach(e,e=>{(0,s.isValidElement)(e)&&r.push(e)}),r}var g=t(5403);let b=e=>{let{children:r,custom:t,initial:n=!0,onExitComplete:i,presenceAffectsLayout:c=!0,mode:d="sync",propagate:u=!1,anchorX:b="left"}=e,[f,v]=(0,h.xQ)(u),w=(0,s.useMemo)(()=>x(r),[r]),j=u&&!f?[]:w.map(p),y=(0,s.useRef)(!0),N=(0,s.useRef)(w),k=(0,l.M)(()=>new Map),[z,L]=(0,s.useState)(w),[C,M]=(0,s.useState)(w);(0,g.E)(()=>{y.current=!1,N.current=w;for(let e=0;e<C.length;e++){let r=p(C[e]);j.includes(r)?k.delete(r):!0!==k.get(r)&&k.set(r,!1)}},[C,j.length,j.join("-")]);let R=[];if(w!==z){let e=[...w];for(let r=0;r<C.length;r++){let t=C[r],a=p(t);j.includes(a)||(e.splice(r,0,t),R.push(t))}return"wait"===d&&R.length&&(e=R),M(x(e)),L(w),null}let{forceRender:B}=(0,s.useContext)(o.L);return(0,a.jsx)(a.Fragment,{children:C.map(e=>{let r=p(e),s=(!u||!!f)&&(w===C||j.includes(r));return(0,a.jsx)(m,{isPresent:s,initial:(!y.current||!!n)&&void 0,custom:t,presenceAffectsLayout:c,mode:d,onExitComplete:s?void 0:()=>{if(!k.has(r))return;k.set(r,!0);let e=!0;k.forEach(r=>{r||(e=!1)}),e&&(null==B||B(),M(N.current),u&&(null==v||v()),i&&i())},anchorX:b,children:e},r)})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[478,441,517,358],()=>r(2204)),_N_E=e.O()}]);