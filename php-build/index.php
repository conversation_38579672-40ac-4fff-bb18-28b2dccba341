<?php
// Configurazioni PHP
ini_set('display_errors', 0); // Disabilita la visualizzazione degli errori in produzione

// Funzione per trovare il percorso corretto per i file statici
function getStaticFilePath($uri)
{
    // Rimuove query string
    $uri = strtok($uri, '?');

    // Gestisce la radice - redirect alla lingua di default
    if ($uri === '/' || $uri === '') {
        // Redirect permanente alla lingua di default (tedesco)
        header('Location: /de/', true, 301);
        exit;
    }

    // Rimuove la / iniziale se presente
    $uri = ltrim($uri, '/');

    // Verifica se il percorso termina con una barra
    if (substr($uri, -1) === '/') {
        // Cerca index.html nella directory
        $path = $uri . 'index.html';
    } else {
        // Controlla se è una directory senza barra finale
        if (is_dir($uri)) {
            // Reindirizza con barra finale
            header('Location: /' . $uri . '/');
            exit;
        }
        $path = $uri;
    }

    // Se il file esiste, lo restituisce
    if (file_exists($path)) {
        return $path;
    }

    // Se non esiste, prova ad aggiungere .html
    if (file_exists($path . '.html')) {
        return $path . '.html';
    }

    // Gestione 404
    header('HTTP/1.0 404 Not Found');
    if (file_exists('404.html')) {
        return '404.html';
    }

    // Fallback se non esiste 404.html
    echo '<h1>404 - Pagina non trovata</h1>';
    exit;
}

// Ottieni l'URI richiesto
$requestUri = $_SERVER['REQUEST_URI'];

// Se è una richiesta API, lascia che il router PHP gestisca
if (strpos($requestUri, '/api/') === 0) {
    // Le richieste API sono già gestite dai file PHP nella cartella /api/
    return false;
}

// Gestione speciale per i file statici di Next.js
if (strpos($requestUri, '/_next/') === 0) {
    $filePath = '.' . $requestUri;
    if (file_exists($filePath)) {
        // Imposta il content type appropriato
        $extension = pathinfo($filePath, PATHINFO_EXTENSION);
        switch ($extension) {
            case 'css':
                header('Content-Type: text/css; charset=utf-8');
                header('Cache-Control: public, max-age=86400');
                break;
            case 'js':
                header('Content-Type: application/javascript; charset=utf-8');
                header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
                header('Pragma: no-cache');
                header('Expires: 0');
                break;
            case 'woff2':
                header('Content-Type: font/woff2');
                header('Cache-Control: public, max-age=31536000');
                break;
            case 'woff':
                header('Content-Type: font/woff');
                header('Cache-Control: public, max-age=31536000');
                break;
        }

        readfile($filePath);
        exit;
    }
}

// Ottieni il percorso del file statico
$filePath = getStaticFilePath($requestUri);

// Imposta il content type in base all'estensione del file
$extension = pathinfo($filePath, PATHINFO_EXTENSION);
switch ($extension) {
    case 'html':
        header('Content-Type: text/html; charset=utf-8');
        break;
    case 'css':
        header('Content-Type: text/css; charset=utf-8');
        break;
    case 'js':
        header('Content-Type: application/javascript; charset=utf-8');
        break;
    case 'json':
        header('Content-Type: application/json; charset=utf-8');
        break;
    case 'svg':
        header('Content-Type: image/svg+xml');
        break;
    case 'png':
        header('Content-Type: image/png');
        break;
    case 'jpg':
    case 'jpeg':
        header('Content-Type: image/jpeg');
        break;
    case 'gif':
        header('Content-Type: image/gif');
        break;
        // Aggiungi altri tipi MIME se necessario
}

// Imposta la cache per i file statici (1 giorno), ma disabilita per JavaScript e file di emergenza
if (in_array($extension, ['css', 'png', 'jpg', 'jpeg', 'gif', 'svg'])) {
    header('Cache-Control: public, max-age=86400');
} else if ($extension === 'js') {
    // Disabilita la cache per i file JavaScript
    header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
    header('Pragma: no-cache');
    header('Expires: 0');
}

// Aggiungi versioning casuale per i file JS relativi alla pagina di emergenza
if (strpos($requestUri, '/emergency') !== false && $extension === 'js') {
    header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
    header('Pragma: no-cache');
    header('Expires: 0');
}

// Servi il file
readfile($filePath);
