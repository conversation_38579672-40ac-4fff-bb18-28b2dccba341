
RewriteEngine On
RewriteBase /

# Redirect root to default language (German)
RewriteRule ^$ /de/ [R=301,L]

# Handle static files directly (CSS, JS, images, fonts)
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule ^(.*)$ $1 [L]

# Handle directories directly
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^(.*)$ $1 [L]

# Aggiungi parametro di invalidazione cache ai file js
RewriteRule ^(_next/static/.*\.js)$ $1?v=1752087711126 [L]

# Regole per gestire le rotte di Next.js
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Set proper MIME types and caching
<FilesMatch "\.(css)$">
    Header set Content-Type "text/css; charset=utf-8"
    Header set Cache-Control "public, max-age=86400"
</FilesMatch>

<FilesMatch "\.(js)$">
    Header set Content-Type "application/javascript; charset=utf-8"
    Header set Cache-Control "no-store, no-cache, must-revalidate, max-age=0"
    Header set Pragma "no-cache"
    Header set Expires "0"
</FilesMatch>

<FilesMatch "\.(woff2)$">
    Header set Content-Type "font/woff2"
    Header set Cache-Control "public, max-age=31536000"
</FilesMatch>

<FilesMatch "\.(woff)$">
    Header set Content-Type "font/woff"
    Header set Cache-Control "public, max-age=31536000"
</FilesMatch>

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE text/html
</IfModule>
