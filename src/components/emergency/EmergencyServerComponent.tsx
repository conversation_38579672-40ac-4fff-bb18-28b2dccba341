import { <PERSON>aPhone, FaW<PERSON>sapp, FaClock, FaShieldAlt, FaTools, FaWater, FaWrench, FaBuilding } from "react-icons/fa";
import Link from "next/link";
import { Translations } from "@/types/translations";

interface EmergencyServerComponentProps {
  data: any;
  t: Translations;
  lang: string;
}

export function EmergencyServerComponent({ data, t, lang }: EmergencyServerComponentProps) {
  return (
    <main className="min-h-screen bg-white">
      {/* Hero Section - Background image with blur effect */}
      <section className="relative h-[80vh] min-h-[500px] overflow-hidden pt-16 flex items-center">
        {/* Background Image with Blur */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: "url('/images/emergency-hero.jpg')",
          }}
        ></div>

        {/* Blur Overlay */}
        <div className="absolute inset-0 backdrop-blur-sm bg-black/40"></div>

        {/* Additional gradient overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/60 via-blue-800/50 to-blue-700/60"></div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight">
              {data.hero.title}
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed">
              {data.hero.subtitle}
            </p>
            <p className="text-lg text-blue-200 mb-12 max-w-4xl mx-auto">
              {data.hero.description}
            </p>
            
            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <a
                href={`tel:${data.cta.emergency_number}`}
                className="inline-flex items-center bg-red-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                <FaPhone className="mr-3 text-xl" />
                {data.cta.emergency_number}
              </a>
              <a
                href={`https://wa.me/${data.cta.whatsapp_number.replace(/\s+/g, '')}`}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center bg-green-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-green-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                <FaWhatsapp className="mr-3 text-xl" />
                WhatsApp
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {data.features.title}
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-8 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <FaClock className="text-white text-2xl" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                {data.features.feature1.title}
              </h3>
              <p className="text-gray-600">
                {data.features.feature1.description}
              </p>
            </div>
            
            <div className="text-center p-8 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <FaShieldAlt className="text-white text-2xl" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                {data.features.feature2.title}
              </h3>
              <p className="text-gray-600">
                {data.features.feature2.description}
              </p>
            </div>
            
            <div className="text-center p-8 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <FaTools className="text-white text-2xl" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                {data.features.feature3.title}
              </h3>
              <p className="text-gray-600">
                {data.features.feature3.description}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {data.services_title}
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {data.services.map((service: any) => (
              <div key={service.id} className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200 hover:shadow-xl transition-shadow duration-300">
                <div className="p-8">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-4">
                      <FaWater className="text-white text-xl" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">
                      {service.title}
                    </h3>
                  </div>
                  <p className="text-gray-600 mb-4">
                    {service.description}
                  </p>
                  <div className="flex items-center text-sm text-green-600 font-semibold">
                    <FaClock className="mr-2" />
                    Tempo di risposta: {service.response_time}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Certificates Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Certificazione Professionale
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Il nostro team è certificato per la gestione e il risanamento dei danni causati dall'acqua, garantendo interventi professionali e di alta qualità.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Certificato 1 */}
            <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200 hover:shadow-xl transition-shadow duration-300">
              <div className="flex flex-col lg:flex-row">
                <div className="flex-1 p-8">
                  <div className="flex items-start gap-3 mb-4">
                    <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                      <FaShieldAlt className="text-white text-lg" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 leading-tight">
                        {lang === 'it' ? 'Certificato Fachberater Wasserschadensanierung' :
                         lang === 'fr' ? 'Certificat Fachberater Wasserschadensanierung' :
                         'Zertifikat Fachberater Wasserschadensanierung'}
                      </h3>
                      <p className="text-sm text-gray-500 mt-1">
                        {lang === 'it' ? 'Rilasciato da Dantherm Academy' :
                         lang === 'fr' ? 'Délivré par Dantherm Academy' :
                         'Ausgestellt von Dantherm Academy'}
                      </p>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-6">
                    {lang === 'it' ? 'Questo certificato attesta la nostra competenza specializzata nella gestione e risanamento dei danni causati dall\'acqua, garantendo interventi professionali secondo i più alti standard del settore.' :
                     lang === 'fr' ? 'Ce certificat atteste notre expertise spécialisée dans la gestion et l\'assainissement des dommages causés par l\'eau, garantissant des interventions professionnelles selon les plus hauts standards de l\'industrie.' :
                     'Dieses Zertifikat bescheinigt unsere Fachkompetenz in der Behandlung von Wasserschäden und gewährleistet professionelle Eingriffe nach den höchsten Branchenstandards.'}
                  </p>
                  <a
                    href="/documents/certificato.pdf"
                    target="_blank"
                    className="inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    {lang === 'it' ? 'Visualizza certificato' :
                     lang === 'fr' ? 'Voir le certificat' :
                     'Zertifikat ansehen'}
                  </a>
                </div>
                <div className="lg:w-48 flex-shrink-0">
                  <div className="h-full bg-gradient-to-br from-gray-50 to-gray-100 p-4 flex items-center justify-center">
                    <a
                      href="/documents/certificato.pdf"
                      target="_blank"
                      className="relative group cursor-pointer block"
                    >
                      <img
                        src="/images/certificates/certificato.png"
                        alt="Certificato Wasserschadensanierung"
                        width={160}
                        height={220}
                        className="rounded-lg shadow-md group-hover:shadow-lg transition-shadow duration-300 border border-gray-200"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300 rounded-lg flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                          </svg>
                        </div>
                      </div>
                    </a>
                  </div>
                </div>
              </div>
            </div>

            {/* Certificato 2 */}
            <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200 hover:shadow-xl transition-shadow duration-300">
              <div className="flex flex-col lg:flex-row">
                <div className="flex-1 p-8">
                  <div className="flex items-start gap-3 mb-4">
                    <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center flex-shrink-0">
                      <FaBuilding className="text-white text-lg" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 leading-tight">
                        {lang === 'it' ? 'Certificato Bautrocknungen' :
                         lang === 'fr' ? 'Certificat Bautrocknungen' :
                         'Zertifikat Bautrocknungen'}
                      </h3>
                      <p className="text-sm text-gray-500 mt-1">
                        {lang === 'it' ? 'Specializzazione in servizi di asciugatura edifici' :
                         lang === 'fr' ? 'Spécialisation en services de séchage de bâtiments' :
                         'Spezialisiert auf Bautrocknungs-Dienstleistungen'}
                      </p>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-6">
                    {lang === 'it' ? 'Questo certificato attesta la nostra competenza specializzata nei servizi di asciugatura degli edifici, garantendo interventi professionali per il trattamento dell\'umidità e l\'asciugatura delle strutture edilizie.' :
                     lang === 'fr' ? 'Ce certificat atteste notre expertise spécialisée dans les services de séchage des bâtiments, garantissant des interventions professionnelles pour le traitement de l\'humidité et le séchage des structures de construction.' :
                     'Dieses Zertifikat bescheinigt unsere Fachkompetenz in Bautrocknungen-Dienstleistungen und gewährleistet professionelle Eingriffe zur Behandlung von Feuchtigkeit und Trocknung von Baustrukturen.'}
                  </p>
                  <a
                    href="/images/certificates/certificato-2.pdf"
                    target="_blank"
                    className="inline-flex items-center bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    {lang === 'it' ? 'Visualizza certificato asciugatura' :
                     lang === 'fr' ? 'Voir le certificat de séchage' :
                     'Bautrocknungen-Zertifikat ansehen'}
                  </a>
                </div>
                <div className="lg:w-48 flex-shrink-0">
                  <div className="h-full bg-gradient-to-br from-gray-50 to-gray-100 p-4 flex items-center justify-center">
                    <a
                      href="/images/certificates/certificato-2.pdf"
                      target="_blank"
                      className="relative group cursor-pointer block"
                    >
                      <img
                        src="/images/certificates/certificato-2.png"
                        alt="Certificato Bautrocknungen"
                        width={160}
                        height={220}
                        className="rounded-lg shadow-md group-hover:shadow-lg transition-shadow duration-300 border border-gray-200"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300 rounded-lg flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                          </svg>
                        </div>
                      </div>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
